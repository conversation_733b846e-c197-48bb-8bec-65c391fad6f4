# Progres implementace rozšíření modulu technologií

## 🎯 Cíl projektu
Rozšířit modul technologií tak, aby každá technologie měla svou vlastní prokliknutelnou podstránku s detailními informacemi a galerií realizací.

## ✅ Krok 1: Rozšíření databázové struktury - **DOKONČENO**

### Co bylo implementováno:
- ✅ Roz<PERSON><PERSON><PERSON><PERSON><PERSON> tabul<PERSON> `ps_technologie` o nové sloupce:
  - `slug` varchar(255) NOT NULL - SEO-friendly URL
  - `detailed_description` longtext - podrobný popis technologie
  - `advantages` text - výhody technologie (oddělené \n)
  - `applications` text - oblasti použití (oddělené \n)
  - `gallery_images` text - JSON pole cest k obrázkům galerie
  - Unique index na `slug`

- ✅ Upgrade skript `sql/upgrade.sql`:
  - Automatické p<PERSON> sloupců do existující databáze
  - Generování slug pro všechny technologie
  - Aktualizace všech 12 technologií s detailními informacemi

- ✅ Aktualizace hlavního modulu `technologie.php`:
  - Verze zvýšena na 1.3.0
  - Rozšíření ObjectModel definice
  - Přidání upgrade() metody s verzováním
  - Aktualizace validačních pravidel

- ✅ Infrastruktura:
  - Adresář `uploads/gallery/` pro galerii obrázků
  - Dokumentace v CHANGELOG.md

### Soubory upravené v kroku 1:
- `modules/technologie/sql/install.sql` - rozšíření CREATE TABLE
- `modules/technologie/sql/upgrade.sql` - nový soubor pro upgrade
- `modules/technologie/technologie.php` - verze, ObjectModel, upgrade metoda
- `modules/technologie/CHANGELOG.md` - dokumentace změn
- `modules/technologie/uploads/gallery/.gitkeep` - nový adresář

---

## 🔄 Krok 2: Aktualizace Doctrine Entity - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Rozšířit `src/Entity/Technologie.php` o nové properties:
  - `private ?string $slug = null`
  - `private ?string $detailedDescription = null`
  - `private ?string $advantages = null`
  - `private ?string $applications = null`
  - `private ?string $galleryImages = null`

- [ ] Přidat Doctrine anotace pro nové sloupce
- [ ] Implementovat gettery a settery pro nové properties
- [ ] Přidat metody pro práci s galerii:
  - `getGalleryImagesArray()` - dekódování JSON
  - `setGalleryImagesArray(array $images)` - kódování do JSON
  - `addGalleryImage(string $imagePath)`
  - `removeGalleryImage(string $imagePath)`

- [ ] Přidat metodu pro generování slug:
  - `generateSlug()` - automatické generování z názvu
  - `getDetailUrl()` - získání URL pro detail stránku

### Soubory k úpravě v kroku 2:
- `modules/technologie/src/Entity/Technologie.php`

---

## 🔄 Krok 3: Aktualizace Repository - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Rozšířit `src/Repository/TechnologieRepository.php`:
  - `findBySlug(string $slug)` - načtení technologie podle slug
  - `findActiveBySlug(string $slug)` - pouze aktivní technologie
  - `getAllSlugs()` - seznam všech slug pro validaci

- [ ] Rozšířit `src/Repository/TechnologieDbRepository.php` (fallback):
  - Stejné metody jako v Doctrine repository
  - Fallback pro případy kdy Doctrine není dostupné

### Soubory k úpravě v kroku 3:
- `modules/technologie/src/Repository/TechnologieRepository.php`
- `modules/technologie/src/Repository/TechnologieDbRepository.php`

---

## 🔄 Krok 4: Implementace detail controlleru - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Dokončit `detailAction()` v `controllers/front/technologie.php`:
  - Načtení technologie podle slug z URL
  - Kontrola existence a aktivního stavu
  - Příprava dat pro šablonu (technologie, galerie, breadcrumb)
  - SEO meta tagy specifické pro technologii
  - Error handling (404 pro neexistující technologie)

- [ ] Přidat metody pro detail:
  - `getTechnologieBySlug(string $slug)`
  - `prepareDetailBreadcrumb(Technologie $tech)`
  - `getDetailMetaTags(Technologie $tech)`

### Soubory k úpravě v kroku 4:
- `modules/technologie/controllers/front/technologie.php`

---

## 🔄 Krok 5: Vytvoření detail šablony - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Vytvořit `views/templates/front/technologie-detail.tpl`:
  - Header s názvem technologie a breadcrumb
  - Hero sekce s hlavním obrázkem a krátkým popisem
  - Detailní popis technologie
  - Sekce výhod (seznam s ikonkami)
  - Sekce oblastí použití
  - Galerie realizací (pokud existují obrázky)
  - Kontaktní sekce s CTA
  - Navigace na další/předchozí technologii

- [ ] Přidat CSS styly pro detail stránku:
  - Responzivní layout
  - Galerie s lightbox efektem
  - Styly pro výhody a aplikace
  - Navigační prvky

- [ ] JavaScript funkcionalita:
  - Lightbox pro galerii
  - Smooth scrolling
  - Lazy loading obrázků

### Soubory k vytvoření v kroku 5:
- `modules/technologie/views/templates/front/technologie-detail.tpl`
- Rozšíření `modules/technologie/views/css/front.css`
- Rozšíření `modules/technologie/views/js/front.js`

---

## 🔄 Krok 6: Aktualizace admin rozhraní - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Rozšířit `src/Form/TechnologieType.php`:
  - Pole pro slug (s automatickým generováním)
  - Textarea pro detailní popis (WYSIWYG editor)
  - Textarea pro výhody (s nápovědou o formátu)
  - Textarea pro oblasti použití
  - Multiple file upload pro galerii

- [ ] Aktualizovat `views/templates/admin/form.tpl`:
  - Nové formulářové prvky
  - JavaScript pro automatické generování slug
  - Preview galerie obrázků
  - Drag & drop pro řazení galerie

- [ ] Rozšířit admin controller:
  - Zpracování upload galerie
  - Validace slug (jedinečnost)
  - Automatické generování slug při uložení

### Soubory k úpravě v kroku 6:
- `modules/technologie/src/Form/TechnologieType.php`
- `modules/technologie/views/templates/admin/form.tpl`
- `modules/technologie/controllers/admin/AdminTechnologieController.php`
- Rozšíření `modules/technologie/views/css/admin.css`
- Rozšíření `modules/technologie/views/js/admin.js`

---

## 🔄 Krok 7: Aktualizace frontend seznamu - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Aktualizovat `views/templates/front/technologie.tpl`:
  - Změnit tlačítko "Více informací" na odkaz
  - Generovat URL pro detail pomocí slug
  - Přidat preview výhod (první 2-3 body)
  - Vylepšit karty technologií

- [ ] Aktualizovat kontroler pro seznam:
  - Příprava URL pro detail stránky
  - Načítání zkrácených výhod pro preview

### Soubory k úpravě v kroku 7:
- `modules/technologie/views/templates/front/technologie.tpl`
- `modules/technologie/controllers/front/technologie.php`

---

## 🔄 Krok 8: Testování a finalizace - **ČEKÁ NA IMPLEMENTACI**

### Co je potřeba udělat:
- [ ] Testování upgrade procesu:
  - Test upgrade z verze 1.2.0 na 1.3.0
  - Kontrola integrity dat po upgrade

- [ ] Testování funkcionalit:
  - Generování a validace slug
  - Funkčnost detail stránek
  - Upload a zobrazení galerie
  - Responzivní design
  - SEO optimalizace

- [ ] Dokumentace:
  - Aktualizace README.md
  - Aktualizace INSTALL.md
  - Testovací checklist

### Soubory k úpravě v kroku 8:
- `modules/technologie/README.md`
- `modules/technologie/INSTALL.md`
- `modules/technologie/TESTING_CHECKLIST.md`

---

## 📋 Celkový přehled souborů k úpravě

### Nové soubory:
- ✅ `modules/technologie/sql/upgrade.sql`
- ✅ `modules/technologie/uploads/gallery/.gitkeep`
- 🔄 `modules/technologie/views/templates/front/technologie-detail.tpl`

### Soubory k úpravě:
- ✅ `modules/technologie/technologie.php` (verze, ObjectModel, upgrade)
- ✅ `modules/technologie/sql/install.sql` (rozšíření tabulky)
- ✅ `modules/technologie/CHANGELOG.md` (dokumentace)
- 🔄 `modules/technologie/src/Entity/Technologie.php` (nové properties)
- 🔄 `modules/technologie/src/Repository/TechnologieRepository.php` (nové metody)
- 🔄 `modules/technologie/src/Repository/TechnologieDbRepository.php` (fallback)
- 🔄 `modules/technologie/controllers/front/technologie.php` (detailAction)
- 🔄 `modules/technologie/src/Form/TechnologieType.php` (nové formulářové prvky)
- 🔄 `modules/technologie/views/templates/admin/form.tpl` (admin rozhraní)
- 🔄 `modules/technologie/views/templates/front/technologie.tpl` (odkazy na detail)
- 🔄 `modules/technologie/views/css/front.css` (styly pro detail)
- 🔄 `modules/technologie/views/js/front.js` (JavaScript pro detail)
- 🔄 `modules/technologie/views/css/admin.css` (admin styly)
- 🔄 `modules/technologie/views/js/admin.js` (admin JavaScript)

---

## 🎯 Priorita dalších kroků

1. **Krok 2** - Aktualizace Entity (nejdůležitější pro fungování)
2. **Krok 3** - Repository metody (potřebné pro načítání dat)
3. **Krok 4** - Detail controller (logika pro zobrazení)
4. **Krok 5** - Detail šablona (frontend zobrazení)
5. **Krok 7** - Aktualizace seznamu (propojení s detailem)
6. **Krok 6** - Admin rozhraní (pro správu obsahu)
7. **Krok 8** - Testování a dokumentace

**Aktuální stav: Krok 1 dokončen ✅, připraven na Krok 2 🔄**
